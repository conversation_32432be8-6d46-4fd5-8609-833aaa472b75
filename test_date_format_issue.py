#!/usr/bin/env python3
"""
Date Format Issue Investigation Script

This script investigates the specific date format issues found in the Devo data
that could be causing the backup system to miss or incorrectly filter data.
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

try:
    from tngd_backup.core.devo_client import DevoClient
    from tngd_backup.core.config_manager import ConfigManager
except ImportError as e:
    print(f"ERROR: Failed to import required modules: {e}")
    sys.exit(1)


class DateFormatInvestigator:
    """Investigate date format issues in Devo data."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the investigator."""
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Load configuration
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.config
        
        # Initialize Devo client
        self.devo_client = DevoClient(self.config_manager)
        
        # Test tables
        self.test_tables = [
            'my.app.tngd.polardb',
            'cloud.office365.management.exchange',
            'firewall.fortinet.traffic.forward'
        ]
    
    def setup_logging(self):
        """Setup logging."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
    
    def investigate_date_fields(self, table_name: str) -> Dict[str, Any]:
        """Investigate date fields in a table."""
        self.logger.info(f"Investigating date fields in {table_name}")
        
        result = {
            'table_name': table_name,
            'date_fields': [],
            'sample_data': {},
            'issues': []
        }
        
        try:
            # Get a sample of data without any date filtering
            query = f"from {table_name} select * limit 5"
            sample_data = self.devo_client.execute_query(query, days=7, timeout=300)
            
            if sample_data:
                # Analyze the first row
                first_row = sample_data[0]
                result['sample_data'] = first_row
                
                # Find all date-related fields
                date_fields = []
                for field_name, field_value in first_row.items():
                    field_name_lower = field_name.lower()
                    if any(keyword in field_name_lower for keyword in ['date', 'time', 'timestamp']):
                        date_fields.append({
                            'field_name': field_name,
                            'field_value': field_value,
                            'field_type': type(field_value).__name__,
                            'is_numeric': isinstance(field_value, (int, float))
                        })
                
                result['date_fields'] = date_fields
                
                # Check if eventdate exists and its format
                if 'eventdate' in first_row:
                    eventdate_value = first_row['eventdate']
                    result['eventdate_analysis'] = {
                        'value': eventdate_value,
                        'type': type(eventdate_value).__name__,
                        'is_numeric': isinstance(eventdate_value, (int, float)),
                        'is_timestamp': self.is_timestamp(eventdate_value),
                        'converted_date': self.convert_timestamp_to_date(eventdate_value) if self.is_timestamp(eventdate_value) else None
                    }
                    
                    # Check if it's a timestamp that needs conversion
                    if self.is_timestamp(eventdate_value):
                        converted_date = self.convert_timestamp_to_date(eventdate_value)
                        result['issues'].append(f"eventdate is a timestamp ({eventdate_value}) that converts to {converted_date}")
                else:
                    result['issues'].append("eventdate field not found in data")
                
                # Test different date filtering approaches
                result['filtering_tests'] = self.test_date_filtering_approaches(table_name)
                
            else:
                result['issues'].append("No sample data found")
                
        except Exception as e:
            result['issues'].append(f"Error investigating table: {str(e)}")
        
        return result
    
    def is_timestamp(self, value) -> bool:
        """Check if a value looks like a timestamp."""
        if isinstance(value, (int, float)):
            # Check if it's a reasonable timestamp (between 1970 and 2100)
            if 0 < value < 4102444800000:  # Jan 1, 2100 in milliseconds
                return True
        return False
    
    def convert_timestamp_to_date(self, timestamp) -> str:
        """Convert timestamp to date string."""
        try:
            # Try milliseconds first
            if timestamp > 1000000000000:  # Likely milliseconds
                dt = datetime.fromtimestamp(timestamp / 1000)
            else:  # Likely seconds
                dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%Y-%m-%d')
        except:
            return "Invalid timestamp"
    
    def test_date_filtering_approaches(self, table_name: str) -> Dict[str, Any]:
        """Test different approaches to date filtering."""
        test_results = {}
        
        # Test 1: No date filter
        try:
            query = f"from {table_name} select count() as total limit 1"
            result = self.devo_client.execute_query(query, days=7, timeout=300)
            test_results['no_filter_count'] = result[0]['total'] if result else 0
        except Exception as e:
            test_results['no_filter_error'] = str(e)
        
        # Test 2: Standard eventdate filter (current approach)
        try:
            query = f"from {table_name} where eventdate = '2025-07-01' select count() as total limit 1"
            result = self.devo_client.execute_query(query, days=1, timeout=300)
            test_results['standard_filter_count'] = result[0]['total'] if result else 0
        except Exception as e:
            test_results['standard_filter_error'] = str(e)
        
        # Test 3: Timestamp range filter (if eventdate is timestamp)
        try:
            # Convert 2025-07-01 to timestamp range
            start_ts = int(datetime(2025, 7, 1).timestamp() * 1000)
            end_ts = int(datetime(2025, 7, 2).timestamp() * 1000)
            
            query = f"from {table_name} where eventdate >= {start_ts} and eventdate < {end_ts} select count() as total limit 1"
            result = self.devo_client.execute_query(query, days=1, timeout=300)
            test_results['timestamp_filter_count'] = result[0]['total'] if result else 0
        except Exception as e:
            test_results['timestamp_filter_error'] = str(e)
        
        # Test 4: Date function approach
        try:
            query = f"from {table_name} where toDate(eventdate) = '2025-07-01' select count() as total limit 1"
            result = self.devo_client.execute_query(query, days=1, timeout=300)
            test_results['date_function_count'] = result[0]['total'] if result else 0
        except Exception as e:
            test_results['date_function_error'] = str(e)
        
        return test_results
    
    def run_investigation(self) -> Dict[str, Any]:
        """Run the complete investigation."""
        self.logger.info("Starting date format investigation")
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'tables_investigated': {},
            'summary': {
                'total_tables': len(self.test_tables),
                'tables_with_timestamp_eventdate': 0,
                'tables_with_string_eventdate': 0,
                'tables_missing_eventdate': 0,
                'recommended_fixes': []
            }
        }
        
        for table_name in self.test_tables:
            self.logger.info(f"\nInvestigating {table_name}")
            table_result = self.investigate_date_fields(table_name)
            results['tables_investigated'][table_name] = table_result
            
            # Update summary
            if 'eventdate_analysis' in table_result:
                if table_result['eventdate_analysis']['is_timestamp']:
                    results['summary']['tables_with_timestamp_eventdate'] += 1
                else:
                    results['summary']['tables_with_string_eventdate'] += 1
            else:
                results['summary']['tables_missing_eventdate'] += 1
        
        # Generate recommendations
        if results['summary']['tables_with_timestamp_eventdate'] > 0:
            results['summary']['recommended_fixes'].append(
                "Update backup system to handle timestamp-based eventdate fields"
            )
            results['summary']['recommended_fixes'].append(
                "Modify date filtering logic to convert dates to timestamp ranges"
            )
        
        if results['summary']['tables_missing_eventdate'] > 0:
            results['summary']['recommended_fixes'].append(
                "Identify alternative date fields for tables missing eventdate"
            )
        
        return results
    
    def print_summary(self, results: Dict[str, Any]):
        """Print investigation summary."""
        print(f"\n{'='*60}")
        print("DATE FORMAT INVESTIGATION SUMMARY")
        print(f"{'='*60}")
        
        summary = results['summary']
        print(f"Total tables investigated: {summary['total_tables']}")
        print(f"Tables with timestamp eventdate: {summary['tables_with_timestamp_eventdate']}")
        print(f"Tables with string eventdate: {summary['tables_with_string_eventdate']}")
        print(f"Tables missing eventdate: {summary['tables_missing_eventdate']}")
        
        print(f"\nDETAILED FINDINGS:")
        for table_name, table_result in results['tables_investigated'].items():
            print(f"\n📊 {table_name}:")
            
            if 'eventdate_analysis' in table_result:
                analysis = table_result['eventdate_analysis']
                print(f"  eventdate value: {analysis['value']}")
                print(f"  eventdate type: {analysis['type']}")
                if analysis['is_timestamp']:
                    print(f"  ⚠️ TIMESTAMP DETECTED - converts to: {analysis['converted_date']}")
                else:
                    print(f"  ✅ String format detected")
            else:
                print(f"  ❌ No eventdate field found")
            
            if 'filtering_tests' in table_result:
                tests = table_result['filtering_tests']
                print(f"  Filtering test results:")
                for test_name, test_result in tests.items():
                    if isinstance(test_result, int):
                        print(f"    {test_name}: {test_result} rows")
                    else:
                        print(f"    {test_name}: ERROR - {test_result}")
            
            if table_result['issues']:
                print(f"  Issues found:")
                for issue in table_result['issues']:
                    print(f"    - {issue}")
        
        if summary['recommended_fixes']:
            print(f"\n🔧 RECOMMENDED FIXES:")
            for i, fix in enumerate(summary['recommended_fixes'], 1):
                print(f"  {i}. {fix}")
        
        print(f"\n{'='*60}")


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Date Format Issue Investigation")
    parser.add_argument('--config', help='Configuration file path')
    parser.add_argument('--output', default='date_investigation_results.json', help='Output file')
    
    args = parser.parse_args()
    
    try:
        investigator = DateFormatInvestigator(args.config)
        results = investigator.run_investigation()
        
        # Save results
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # Print summary
        investigator.print_summary(results)
        
        # Return appropriate exit code
        if results['summary']['tables_with_timestamp_eventdate'] > 0:
            print(f"\n⚠️ CRITICAL: Found timestamp-based eventdate fields that need fixing!")
            return 1
        else:
            print(f"\n✅ No critical date format issues found")
            return 0
        
    except Exception as e:
        print(f"❌ Investigation failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
