#!/usr/bin/env python3
"""
Devo Data Verification Test Script

This script tests the actual raw data in Devo tables to identify potential issues
that could cause data loss or incorrect backup results.

Features:
- Tests table existence and accessibility
- Verifies data availability for specific dates
- Checks for data consistency issues
- Identifies potential query problems
- Compares expected vs actual row counts
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

try:
    from tngd_backup.core.devo_client import DevoClient
    from tngd_backup.core.config_manager import ConfigManager
except ImportError as e:
    print(f"ERROR: Failed to import required modules: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)


class DevoDataVerifier:
    """Comprehensive Devo data verification tool."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the verifier."""
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Load configuration
        try:
            self.config_manager = ConfigManager(config_path)
            self.config = self.config_manager.config
            self.logger.info("Configuration loaded successfully")
        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
            raise
        
        # Initialize Devo client
        try:
            self.devo_client = DevoClient(self.config_manager)
            self.logger.info("Devo client initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize Devo client: {e}")
            raise
        
        # Load tables list
        self.tables = self.load_tables()
        self.logger.info(f"Loaded {len(self.tables)} tables for verification")
        
        # Test results storage
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'total_tables': len(self.tables),
            'table_tests': {},
            'summary': {
                'accessible_tables': 0,
                'inaccessible_tables': 0,
                'tables_with_data': 0,
                'tables_without_data': 0,
                'total_rows_found': 0,
                'potential_issues': []
            }
        }
    
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('devo_verification.log')
            ]
        )
    
    def load_tables(self) -> List[str]:
        """Load table list from configuration."""
        try:
            tables_file = Path("config/tables.json")
            if tables_file.exists():
                with open(tables_file, 'r') as f:
                    tables = json.load(f)
                return tables
            else:
                # Fallback tables for testing
                return [
                    'my.app.tngd.polardb',
                    'cloud.office365.management.exchange',
                    'firewall.fortinet.traffic.forward',
                    'cef0.zscaler.nssweblog',
                    'cloud.alibaba.log_service.events'
                ]
        except Exception as e:
            self.logger.error(f"Failed to load tables: {e}")
            return []
    
    def test_table_accessibility(self, table_name: str) -> Dict[str, Any]:
        """Test if a table is accessible and queryable."""
        test_result = {
            'table_name': table_name,
            'accessible': False,
            'error': None,
            'test_query_time': None
        }
        
        try:
            self.logger.info(f"Testing accessibility for table: {table_name}")
            start_time = datetime.now()
            
            # Test with a simple query to check table existence
            exists = self.devo_client.check_table_exists(table_name)
            
            end_time = datetime.now()
            test_result['test_query_time'] = (end_time - start_time).total_seconds()
            test_result['accessible'] = exists
            
            if exists:
                self.logger.info(f"✅ Table {table_name} is accessible")
            else:
                self.logger.warning(f"❌ Table {table_name} is not accessible")
                
        except Exception as e:
            test_result['error'] = str(e)
            self.logger.error(f"❌ Error testing table {table_name}: {e}")
        
        return test_result
    
    def test_data_availability(self, table_name: str, test_date: str) -> Dict[str, Any]:
        """Test data availability for a specific date."""
        test_result = {
            'table_name': table_name,
            'test_date': test_date,
            'has_data': False,
            'row_count': 0,
            'sample_data': None,
            'query_time': None,
            'error': None
        }
        
        try:
            self.logger.info(f"Testing data availability for {table_name} on {test_date}")
            start_time = datetime.now()
            
            # Query with date filter (same as backup system)
            where_clause = f"eventdate = '{test_date}'"
            query = f"from {table_name} where {where_clause} select * limit 10"
            
            results = self.devo_client.execute_query(
                query=query,
                days=1,
                timeout=300,  # 5 minute timeout for testing
                table_name=table_name
            )
            
            end_time = datetime.now()
            test_result['query_time'] = (end_time - start_time).total_seconds()
            test_result['row_count'] = len(results)
            test_result['has_data'] = len(results) > 0
            
            if results:
                # Store sample data (first row, limited fields)
                sample = results[0]
                test_result['sample_data'] = {
                    key: str(value)[:100] if isinstance(value, str) else value
                    for key, value in list(sample.items())[:5]  # First 5 fields only
                }
                self.logger.info(f"✅ Found {len(results)} rows for {table_name} on {test_date}")
            else:
                self.logger.warning(f"⚠️ No data found for {table_name} on {test_date}")
                
        except Exception as e:
            test_result['error'] = str(e)
            self.logger.error(f"❌ Error querying {table_name} for {test_date}: {e}")
        
        return test_result
    
    def test_row_count_accuracy(self, table_name: str, test_date: str) -> Dict[str, Any]:
        """Test row count accuracy using different query methods."""
        test_result = {
            'table_name': table_name,
            'test_date': test_date,
            'count_query_result': 0,
            'sample_query_result': 0,
            'count_matches': False,
            'error': None
        }
        
        try:
            self.logger.info(f"Testing row count accuracy for {table_name} on {test_date}")
            
            # Method 1: Count query
            where_clause = f"eventdate = '{test_date}'"
            count_query = f"from {table_name} where {where_clause} select count() as total_count"
            
            count_results = self.devo_client.execute_query(
                query=count_query,
                days=1,
                timeout=300,
                table_name=table_name
            )
            
            if count_results and 'total_count' in count_results[0]:
                test_result['count_query_result'] = int(count_results[0]['total_count'])
            
            # Method 2: Sample query with larger limit
            sample_query = f"from {table_name} where {where_clause} select * limit 1000"
            
            sample_results = self.devo_client.execute_query(
                query=sample_query,
                days=1,
                timeout=300,
                table_name=table_name
            )
            
            test_result['sample_query_result'] = len(sample_results)
            
            # Compare results
            if test_result['count_query_result'] > 0:
                if test_result['sample_query_result'] == min(1000, test_result['count_query_result']):
                    test_result['count_matches'] = True
                    self.logger.info(f"✅ Row count verification passed for {table_name}")
                else:
                    self.logger.warning(f"⚠️ Row count mismatch for {table_name}: count={test_result['count_query_result']}, sample={test_result['sample_query_result']}")
            
        except Exception as e:
            test_result['error'] = str(e)
            self.logger.error(f"❌ Error in row count test for {table_name}: {e}")
        
        return test_result
    
    def run_comprehensive_test(self, test_dates: List[str] = None) -> Dict[str, Any]:
        """Run comprehensive verification tests."""
        if test_dates is None:
            # Test yesterday and today by default
            today = datetime.now()
            yesterday = today - timedelta(days=1)
            test_dates = [
                yesterday.strftime('%Y-%m-%d'),
                today.strftime('%Y-%m-%d')
            ]
        
        self.logger.info(f"Starting comprehensive verification for {len(self.tables)} tables")
        self.logger.info(f"Test dates: {test_dates}")
        
        for table_name in self.tables:
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"Testing table: {table_name}")
            self.logger.info(f"{'='*60}")
            
            table_result = {
                'accessibility_test': None,
                'data_availability_tests': {},
                'row_count_tests': {},
                'issues_found': []
            }
            
            # Test 1: Table accessibility
            accessibility_result = self.test_table_accessibility(table_name)
            table_result['accessibility_test'] = accessibility_result
            
            if accessibility_result['accessible']:
                self.results['summary']['accessible_tables'] += 1
                
                # Test 2: Data availability for each date
                has_data_any_date = False
                for test_date in test_dates:
                    data_result = self.test_data_availability(table_name, test_date)
                    table_result['data_availability_tests'][test_date] = data_result
                    
                    if data_result['has_data']:
                        has_data_any_date = True
                        self.results['summary']['total_rows_found'] += data_result['row_count']
                        
                        # Test 3: Row count accuracy
                        count_result = self.test_row_count_accuracy(table_name, test_date)
                        table_result['row_count_tests'][test_date] = count_result
                        
                        if not count_result['count_matches'] and count_result['count_query_result'] > 0:
                            issue = f"Row count mismatch in {table_name} on {test_date}"
                            table_result['issues_found'].append(issue)
                            self.results['summary']['potential_issues'].append(issue)
                
                if has_data_any_date:
                    self.results['summary']['tables_with_data'] += 1
                else:
                    self.results['summary']['tables_without_data'] += 1
                    issue = f"No data found in {table_name} for any test date"
                    table_result['issues_found'].append(issue)
                    self.results['summary']['potential_issues'].append(issue)
            else:
                self.results['summary']['inaccessible_tables'] += 1
                issue = f"Table {table_name} is not accessible"
                table_result['issues_found'].append(issue)
                self.results['summary']['potential_issues'].append(issue)
            
            self.results['table_tests'][table_name] = table_result
        
        return self.results
    
    def save_results(self, output_file: str = "devo_verification_results.json"):
        """Save test results to file."""
        try:
            with open(output_file, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)
            self.logger.info(f"Results saved to {output_file}")
        except Exception as e:
            self.logger.error(f"Failed to save results: {e}")
    
    def print_summary(self):
        """Print test summary."""
        summary = self.results['summary']
        
        print(f"\n{'='*60}")
        print("DEVO DATA VERIFICATION SUMMARY")
        print(f"{'='*60}")
        print(f"Total tables tested: {self.results['total_tables']}")
        print(f"Accessible tables: {summary['accessible_tables']}")
        print(f"Inaccessible tables: {summary['inaccessible_tables']}")
        print(f"Tables with data: {summary['tables_with_data']}")
        print(f"Tables without data: {summary['tables_without_data']}")
        print(f"Total rows found: {summary['total_rows_found']:,}")
        print(f"Potential issues: {len(summary['potential_issues'])}")
        
        if summary['potential_issues']:
            print(f"\n⚠️ ISSUES FOUND:")
            for i, issue in enumerate(summary['potential_issues'], 1):
                print(f"  {i}. {issue}")
        else:
            print(f"\n✅ No issues found!")


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Devo Data Verification Tool")
    parser.add_argument('--config', help='Configuration file path')
    parser.add_argument('--dates', nargs='+', help='Test dates (YYYY-MM-DD format)')
    parser.add_argument('--output', default='devo_verification_results.json', help='Output file for results')
    
    args = parser.parse_args()
    
    try:
        # Initialize verifier
        verifier = DevoDataVerifier(args.config)
        
        # Run tests
        results = verifier.run_comprehensive_test(args.dates)
        
        # Save and display results
        verifier.save_results(args.output)
        verifier.print_summary()
        
        # Exit with appropriate code
        if results['summary']['potential_issues']:
            print(f"\n❌ Verification completed with {len(results['summary']['potential_issues'])} issues found")
            return 1
        else:
            print(f"\n✅ Verification completed successfully - no issues found")
            return 0
            
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
