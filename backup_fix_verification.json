{"timestamp": "2025-07-02T20:10:39.353898", "test_date": "2025-06-25", "table_tests": {"my.app.tngd.polardb": {"table_name": "my.app.tngd.polardb", "test_date": "2025-06-25", "old_method_results": {"row_count": 1, "query": "from my.app.tngd.polardb where eventdate = '2025-06-25' select * limit 10", "success": true}, "new_method_results": {"row_count": 1, "query": "from my.app.tngd.polardb where eventdate >= 1750780800000 and eventdate <= 1750867199000 select * limit 10", "success": true, "timestamp_range": "1750780800000 to 1750867199000", "sample_eventdate": 1750809600123, "eventdate_in_range": true}, "fix_working": false, "issues": ["New method not significantly better: old=1, new=1"]}, "cloud.office365.management.exchange": {"table_name": "cloud.office365.management.exchange", "test_date": "2025-06-25", "old_method_results": {"row_count": 1, "query": "from cloud.office365.management.exchange where eventdate = '2025-06-25' select * limit 10", "success": true}, "new_method_results": {"row_count": 1, "query": "from cloud.office365.management.exchange where eventdate >= 1750780800000 and eventdate <= 1750867199000 select * limit 10", "success": true, "timestamp_range": "1750780800000 to 1750867199000", "sample_eventdate": 1750809645866, "eventdate_in_range": true}, "fix_working": false, "issues": ["New method not significantly better: old=1, new=1"]}, "firewall.fortinet.traffic.forward": {"table_name": "firewall.fortinet.traffic.forward", "test_date": "2025-06-25", "old_method_results": {"row_count": 1, "query": "from firewall.fortinet.traffic.forward where eventdate = '2025-06-25' select * limit 10", "success": true}, "new_method_results": {"row_count": 97, "query": "from firewall.fortinet.traffic.forward where eventdate >= 1750780800000 and eventdate <= 1750867199000 select * limit 10", "success": true, "timestamp_range": "1750780800000 to 1750867199000", "sample_eventdate": 1750809600162, "eventdate_in_range": true}, "fix_working": true, "issues": []}}, "backup_engine_test": {"test_date": "2025-06-25", "backup_engine_tests": {"my.app.tngd.polardb": {"table_name": "my.app.tngd.polardb", "query_constructed": "eventdate >= 1750780800000 and eventdate <= 1750867199000", "data_retrieved": true, "row_count": 1, "issues": []}}, "integration_working": true, "issues": []}, "summary": {"total_tables_tested": 3, "tables_with_working_fix": 1, "backup_engine_working": true, "overall_fix_status": "SUCCESS", "issues_found": ["New method not significantly better: old=1, new=1", "New method not significantly better: old=1, new=1"]}}