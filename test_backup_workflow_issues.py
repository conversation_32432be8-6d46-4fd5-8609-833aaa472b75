#!/usr/bin/env python3
"""
Backup Workflow Issue Detection Script

This script specifically tests for issues in the backup workflow that could
cause data loss or incorrect backup results, focusing on the problems shown
in the screenshot.

Key areas tested:
- Date filtering logic
- Query construction issues
- Chunking and pagination problems
- Data retrieval accuracy
- File output verification
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

try:
    from tngd_backup.core.devo_client import DevoClient
    from tngd_backup.core.config_manager import ConfigManager
    from tngd_backup.core.backup_engine import BackupEngine
except ImportError as e:
    print(f"ERROR: Failed to import required modules: {e}")
    sys.exit(1)


class BackupWorkflowTester:
    """Test backup workflow for potential data loss issues."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the tester."""
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Load configuration
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.config
        
        # Initialize Devo client
        self.devo_client = DevoClient(self.config_manager)
        
        # Test results
        self.issues_found = []
        self.test_results = {}
    
    def setup_logging(self):
        """Setup logging."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
    
    def test_date_filtering_logic(self, table_name: str, test_date: str) -> Dict[str, Any]:
        """Test if date filtering is working correctly."""
        self.logger.info(f"Testing date filtering for {table_name} on {test_date}")
        
        result = {
            'test_name': 'date_filtering',
            'table_name': table_name,
            'test_date': test_date,
            'issues': [],
            'data': {}
        }
        
        try:
            # Test 1: Direct query with eventdate filter (as used in backup)
            backup_query = f"from {table_name} where eventdate = '{test_date}' select * limit 100"
            backup_results = self.devo_client.execute_query(backup_query, days=1, timeout=300)
            result['data']['backup_query_rows'] = len(backup_results)
            
            # Test 2: Query without date filter to see total data
            total_query = f"from {table_name} select * limit 100"
            total_results = self.devo_client.execute_query(total_query, days=1, timeout=300)
            result['data']['total_query_rows'] = len(total_results)
            
            # Test 3: Check if eventdate field exists and has correct format
            if backup_results:
                sample_row = backup_results[0]
                if 'eventdate' in sample_row:
                    eventdate_value = sample_row['eventdate']
                    result['data']['eventdate_sample'] = str(eventdate_value)
                    
                    # Check if eventdate matches our filter
                    if str(eventdate_value) != test_date:
                        result['issues'].append(f"eventdate value '{eventdate_value}' doesn't match filter '{test_date}'")
                else:
                    result['issues'].append("eventdate field not found in results")
            
            # Test 4: Alternative date field check
            if backup_results:
                sample_row = backup_results[0]
                date_fields = [k for k in sample_row.keys() if 'date' in k.lower() or 'time' in k.lower()]
                result['data']['date_fields_found'] = date_fields
                
                if not date_fields:
                    result['issues'].append("No date/time fields found in data")
            
            # Issue detection
            if result['data']['backup_query_rows'] == 0 and result['data']['total_query_rows'] > 0:
                result['issues'].append("Date filter returns no data but table has data - possible date filtering issue")
            
        except Exception as e:
            result['issues'].append(f"Query error: {str(e)}")
        
        return result
    
    def test_chunking_logic(self, table_name: str, test_date: str) -> Dict[str, Any]:
        """Test if chunking/pagination is working correctly."""
        self.logger.info(f"Testing chunking logic for {table_name}")
        
        result = {
            'test_name': 'chunking_logic',
            'table_name': table_name,
            'test_date': test_date,
            'issues': [],
            'data': {}
        }
        
        try:
            where_clause = f"eventdate = '{test_date}'"
            
            # Test 1: Get total count
            count_query = f"from {table_name} where {where_clause} select count() as total"
            count_results = self.devo_client.execute_query(count_query, days=1, timeout=300)
            
            if count_results and 'total' in count_results[0]:
                total_count = int(count_results[0]['total'])
                result['data']['total_count'] = total_count
                
                if total_count > 0:
                    # Test 2: Get first chunk
                    chunk1_query = f"from {table_name} where {where_clause} select * limit 50 offset 0"
                    chunk1_results = self.devo_client.execute_query(chunk1_query, days=1, timeout=300)
                    result['data']['chunk1_rows'] = len(chunk1_results)
                    
                    # Test 3: Get second chunk
                    chunk2_query = f"from {table_name} where {where_clause} select * limit 50 offset 50"
                    chunk2_results = self.devo_client.execute_query(chunk2_query, days=1, timeout=300)
                    result['data']['chunk2_rows'] = len(chunk2_results)
                    
                    # Test 4: Check for overlapping data
                    if chunk1_results and chunk2_results:
                        # Compare first row of chunk2 with last row of chunk1
                        if len(chunk1_results) == 50 and len(chunk2_results) > 0:
                            # Check if there's any overlap (this would indicate an issue)
                            chunk1_ids = set()
                            chunk2_ids = set()
                            
                            # Try to find unique identifiers
                            for row in chunk1_results:
                                row_id = str(sorted(row.items()))
                                chunk1_ids.add(row_id)
                            
                            for row in chunk2_results:
                                row_id = str(sorted(row.items()))
                                chunk2_ids.add(row_id)
                            
                            overlap = chunk1_ids.intersection(chunk2_ids)
                            if overlap:
                                result['issues'].append(f"Found {len(overlap)} overlapping rows between chunks")
                    
                    # Issue detection
                    expected_chunk1_size = min(50, total_count)
                    if result['data']['chunk1_rows'] != expected_chunk1_size:
                        result['issues'].append(f"Chunk 1 size mismatch: expected {expected_chunk1_size}, got {result['data']['chunk1_rows']}")
                    
                    if total_count > 50:
                        expected_chunk2_size = min(50, total_count - 50)
                        if result['data']['chunk2_rows'] != expected_chunk2_size:
                            result['issues'].append(f"Chunk 2 size mismatch: expected {expected_chunk2_size}, got {result['data']['chunk2_rows']}")
            else:
                result['issues'].append("Could not get total count")
                
        except Exception as e:
            result['issues'].append(f"Chunking test error: {str(e)}")
        
        return result
    
    def test_backup_engine_query_construction(self, table_name: str, test_date: str) -> Dict[str, Any]:
        """Test the actual query construction used by backup engine."""
        self.logger.info(f"Testing backup engine query construction for {table_name}")
        
        result = {
            'test_name': 'backup_engine_queries',
            'table_name': table_name,
            'test_date': test_date,
            'issues': [],
            'data': {}
        }
        
        try:
            # Simulate the exact query construction from backup_engine.py
            where_clause = f"eventdate = '{test_date}'"
            
            # Test the query_table_to_file method logic
            chunk_size = 50000
            offset = 0
            
            # This mimics the _build_secure_query method
            base_query = f"from {table_name}"
            if where_clause:
                base_query += f" where {where_clause}"
            base_query += " select *"
            
            # Add limit and offset (as done in execute_chunked_query)
            chunked_query = f"{base_query} limit {chunk_size} offset {offset}"
            
            result['data']['constructed_query'] = chunked_query
            
            # Test the constructed query
            query_results = self.devo_client.execute_query(chunked_query, days=1, timeout=300)
            result['data']['query_result_count'] = len(query_results)
            
            # Test if the query syntax is valid
            if query_results is not None:
                result['data']['query_syntax_valid'] = True
            else:
                result['issues'].append("Query returned None - possible syntax error")
            
            # Test date range extraction (potential issue area)
            extracted_date = self.devo_client._extract_date_range_from_query(chunked_query)
            result['data']['extracted_date_range'] = extracted_date
            
            # Test query syntax fixing
            fixed_query = self.devo_client._fix_query_syntax(chunked_query)
            result['data']['fixed_query'] = fixed_query
            
            if fixed_query != chunked_query:
                result['issues'].append("Query was modified by _fix_query_syntax - potential issue")
            
        except Exception as e:
            result['issues'].append(f"Backup engine query test error: {str(e)}")
        
        return result
    
    def test_sample_tables(self, test_date: str = None) -> Dict[str, Any]:
        """Test a sample of tables for common issues."""
        if test_date is None:
            test_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        # Test a subset of tables that are likely to have data
        test_tables = [
            'my.app.tngd.polardb',
            'cloud.office365.management.exchange',
            'firewall.fortinet.traffic.forward'
        ]
        
        self.logger.info(f"Testing sample tables for date: {test_date}")
        
        all_results = {}
        
        for table_name in test_tables:
            self.logger.info(f"\nTesting table: {table_name}")
            
            table_results = {
                'table_name': table_name,
                'test_date': test_date,
                'tests': {}
            }
            
            # Run all tests for this table
            table_results['tests']['date_filtering'] = self.test_date_filtering_logic(table_name, test_date)
            table_results['tests']['chunking'] = self.test_chunking_logic(table_name, test_date)
            table_results['tests']['backup_engine'] = self.test_backup_engine_query_construction(table_name, test_date)
            
            # Collect issues
            table_issues = []
            for test_name, test_result in table_results['tests'].items():
                table_issues.extend(test_result.get('issues', []))
            
            table_results['total_issues'] = len(table_issues)
            table_results['issues'] = table_issues
            
            all_results[table_name] = table_results
            
            # Log summary for this table
            if table_issues:
                self.logger.warning(f"❌ Found {len(table_issues)} issues in {table_name}")
                for issue in table_issues:
                    self.logger.warning(f"  - {issue}")
            else:
                self.logger.info(f"✅ No issues found in {table_name}")
        
        return all_results
    
    def print_summary(self, results: Dict[str, Any]):
        """Print test summary."""
        print(f"\n{'='*60}")
        print("BACKUP WORKFLOW ISSUE DETECTION SUMMARY")
        print(f"{'='*60}")
        
        total_issues = 0
        total_tables = len(results)
        
        for table_name, table_result in results.items():
            issues_count = table_result.get('total_issues', 0)
            total_issues += issues_count
            
            status = "❌" if issues_count > 0 else "✅"
            print(f"{status} {table_name}: {issues_count} issues")
            
            if issues_count > 0:
                for issue in table_result.get('issues', []):
                    print(f"    - {issue}")
        
        print(f"\nTotal tables tested: {total_tables}")
        print(f"Total issues found: {total_issues}")
        
        if total_issues == 0:
            print("\n✅ No workflow issues detected!")
        else:
            print(f"\n⚠️ Found {total_issues} potential workflow issues that could cause data loss!")


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Backup Workflow Issue Detection")
    parser.add_argument('--config', help='Configuration file path')
    parser.add_argument('--date', help='Test date (YYYY-MM-DD format)')
    parser.add_argument('--output', default='workflow_test_results.json', help='Output file')
    
    args = parser.parse_args()
    
    try:
        tester = BackupWorkflowTester(args.config)
        results = tester.test_sample_tables(args.date)
        
        # Save results
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # Print summary
        tester.print_summary(results)
        
        # Return appropriate exit code
        total_issues = sum(r.get('total_issues', 0) for r in results.values())
        return 1 if total_issues > 0 else 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
