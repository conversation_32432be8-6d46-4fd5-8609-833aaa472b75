#!/usr/bin/env python3
"""
Timestamp Investigation Script

This script investigates why the new timestamp filtering is returning fewer results
than the old string filtering, which suggests there might be data format issues.
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

try:
    from tngd_backup.core.devo_client import DevoClient
    from tngd_backup.core.config_manager import ConfigManager
except ImportError as e:
    print(f"ERROR: Failed to import required modules: {e}")
    sys.exit(1)


class TimestampInvestigator:
    """Investigate timestamp conversion and filtering issues."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the investigator."""
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Load configuration
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.config
        
        # Initialize Devo client
        self.devo_client = DevoClient(self.config_manager)
    
    def setup_logging(self):
        """Setup logging."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
    
    def analyze_eventdate_values(self, table_name: str, sample_size: int = 20) -> Dict[str, Any]:
        """Analyze the actual eventdate values in a table."""
        self.logger.info(f"Analyzing eventdate values in {table_name}")
        
        result = {
            'table_name': table_name,
            'sample_eventdates': [],
            'eventdate_types': {},
            'date_ranges_found': [],
            'issues': []
        }
        
        try:
            # Get a sample of data without any filtering
            query = f"from {table_name} select eventdate limit {sample_size}"
            sample_data = self.devo_client.execute_query(query, days=7, timeout=300)
            
            if sample_data:
                for i, row in enumerate(sample_data):
                    if 'eventdate' in row:
                        eventdate_value = row['eventdate']
                        
                        # Store sample
                        sample_info = {
                            'row_index': i,
                            'value': eventdate_value,
                            'type': type(eventdate_value).__name__,
                            'converted_date': None,
                            'is_timestamp': False
                        }
                        
                        # Try to convert if it's a timestamp
                        if isinstance(eventdate_value, (int, float)):
                            sample_info['is_timestamp'] = True
                            try:
                                # Try milliseconds first
                                if eventdate_value > 1000000000000:  # Likely milliseconds
                                    dt = datetime.fromtimestamp(eventdate_value / 1000)
                                else:  # Likely seconds
                                    dt = datetime.fromtimestamp(eventdate_value)
                                sample_info['converted_date'] = dt.strftime('%Y-%m-%d %H:%M:%S')
                            except:
                                sample_info['converted_date'] = "Invalid timestamp"
                        elif isinstance(eventdate_value, str):
                            sample_info['converted_date'] = eventdate_value
                        
                        result['sample_eventdates'].append(sample_info)
                        
                        # Count types
                        value_type = type(eventdate_value).__name__
                        result['eventdate_types'][value_type] = result['eventdate_types'].get(value_type, 0) + 1
                
                # Extract unique date ranges
                unique_dates = set()
                for sample in result['sample_eventdates']:
                    if sample['converted_date'] and sample['converted_date'] != "Invalid timestamp":
                        if sample['is_timestamp']:
                            date_part = sample['converted_date'].split(' ')[0]
                            unique_dates.add(date_part)
                        else:
                            unique_dates.add(sample['converted_date'])
                
                result['date_ranges_found'] = sorted(list(unique_dates))
                
                self.logger.info(f"Found {len(result['sample_eventdates'])} eventdate samples")
                self.logger.info(f"Types found: {result['eventdate_types']}")
                self.logger.info(f"Date ranges: {result['date_ranges_found']}")
                
            else:
                result['issues'].append("No sample data found")
                
        except Exception as e:
            result['issues'].append(f"Error analyzing eventdate values: {str(e)}")
            self.logger.error(f"Error analyzing {table_name}: {e}")
        
        return result
    
    def test_filtering_methods(self, table_name: str, target_date: str) -> Dict[str, Any]:
        """Test different filtering methods to understand the discrepancy."""
        self.logger.info(f"Testing filtering methods for {table_name} on {target_date}")
        
        result = {
            'table_name': table_name,
            'target_date': target_date,
            'filtering_tests': {},
            'issues': []
        }
        
        try:
            # Method 1: String equality (old method)
            try:
                query1 = f"from {table_name} where eventdate = '{target_date}' select * limit 50"
                results1 = self.devo_client.execute_query(query1, days=1, timeout=300)
                result['filtering_tests']['string_equality'] = {
                    'query': query1,
                    'row_count': len(results1),
                    'sample_eventdates': [r.get('eventdate') for r in results1[:5]] if results1 else []
                }
            except Exception as e:
                result['filtering_tests']['string_equality'] = {'error': str(e)}
            
            # Method 2: Timestamp range (new method)
            try:
                target_dt = datetime.strptime(target_date, '%Y-%m-%d')
                start_timestamp = int(target_dt.timestamp() * 1000)
                end_timestamp = int((target_dt.replace(hour=23, minute=59, second=59)).timestamp() * 1000)
                
                query2 = f"from {table_name} where eventdate >= {start_timestamp} and eventdate <= {end_timestamp} select * limit 50"
                results2 = self.devo_client.execute_query(query2, days=1, timeout=300)
                result['filtering_tests']['timestamp_range'] = {
                    'query': query2,
                    'row_count': len(results2),
                    'timestamp_range': f"{start_timestamp} to {end_timestamp}",
                    'sample_eventdates': [r.get('eventdate') for r in results2[:5]] if results2 else []
                }
            except Exception as e:
                result['filtering_tests']['timestamp_range'] = {'error': str(e)}
            
            # Method 3: String contains (to see if there are string dates)
            try:
                query3 = f"from {table_name} where eventdate like '%{target_date}%' select * limit 50"
                results3 = self.devo_client.execute_query(query3, days=1, timeout=300)
                result['filtering_tests']['string_contains'] = {
                    'query': query3,
                    'row_count': len(results3),
                    'sample_eventdates': [r.get('eventdate') for r in results3[:5]] if results3 else []
                }
            except Exception as e:
                result['filtering_tests']['string_contains'] = {'error': str(e)}
            
            # Method 4: Date function approach
            try:
                query4 = f"from {table_name} where toDate(eventdate) = '{target_date}' select * limit 50"
                results4 = self.devo_client.execute_query(query4, days=1, timeout=300)
                result['filtering_tests']['date_function'] = {
                    'query': query4,
                    'row_count': len(results4),
                    'sample_eventdates': [r.get('eventdate') for r in results4[:5]] if results4 else []
                }
            except Exception as e:
                result['filtering_tests']['date_function'] = {'error': str(e)}
            
            # Method 5: Broader timestamp range (in case of timezone issues)
            try:
                target_dt = datetime.strptime(target_date, '%Y-%m-%d')
                # Add some buffer for timezone issues
                start_timestamp = int((target_dt - timedelta(hours=12)).timestamp() * 1000)
                end_timestamp = int((target_dt + timedelta(hours=36)).timestamp() * 1000)
                
                query5 = f"from {table_name} where eventdate >= {start_timestamp} and eventdate <= {end_timestamp} select * limit 50"
                results5 = self.devo_client.execute_query(query5, days=1, timeout=300)
                result['filtering_tests']['broad_timestamp_range'] = {
                    'query': query5,
                    'row_count': len(results5),
                    'timestamp_range': f"{start_timestamp} to {end_timestamp}",
                    'sample_eventdates': [r.get('eventdate') for r in results5[:5]] if results5 else []
                }
            except Exception as e:
                result['filtering_tests']['broad_timestamp_range'] = {'error': str(e)}
            
        except Exception as e:
            result['issues'].append(f"Error testing filtering methods: {str(e)}")
        
        return result
    
    def run_investigation(self, target_date: str = "2025-06-25") -> Dict[str, Any]:
        """Run complete timestamp investigation."""
        self.logger.info(f"Starting timestamp investigation for {target_date}")
        
        test_tables = [
            'my.app.tngd.polardb',
            'cloud.office365.management.exchange',
            'firewall.fortinet.traffic.forward'
        ]
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'target_date': target_date,
            'table_analyses': {},
            'summary': {
                'tables_with_mixed_formats': 0,
                'tables_with_timestamp_only': 0,
                'tables_with_string_only': 0,
                'recommended_approach': None,
                'issues_found': []
            }
        }
        
        for table_name in test_tables:
            self.logger.info(f"\nAnalyzing {table_name}")
            
            # Analyze eventdate values
            eventdate_analysis = self.analyze_eventdate_values(table_name)
            
            # Test filtering methods
            filtering_analysis = self.test_filtering_methods(table_name, target_date)
            
            table_result = {
                'eventdate_analysis': eventdate_analysis,
                'filtering_analysis': filtering_analysis
            }
            
            results['table_analyses'][table_name] = table_result
            
            # Update summary
            types_found = eventdate_analysis.get('eventdate_types', {})
            if len(types_found) > 1:
                results['summary']['tables_with_mixed_formats'] += 1
            elif 'int' in types_found or 'float' in types_found:
                results['summary']['tables_with_timestamp_only'] += 1
            elif 'str' in types_found:
                results['summary']['tables_with_string_only'] += 1
            
            # Collect issues
            results['summary']['issues_found'].extend(eventdate_analysis.get('issues', []))
            results['summary']['issues_found'].extend(filtering_analysis.get('issues', []))
        
        # Determine recommended approach
        if results['summary']['tables_with_timestamp_only'] > 0:
            if results['summary']['tables_with_string_only'] > 0:
                results['summary']['recommended_approach'] = "HYBRID: Support both timestamp and string filtering"
            else:
                results['summary']['recommended_approach'] = "TIMESTAMP_ONLY: Use timestamp range filtering"
        else:
            results['summary']['recommended_approach'] = "STRING_ONLY: Use string equality filtering"
        
        return results
    
    def print_summary(self, results: Dict[str, Any]):
        """Print investigation summary."""
        print(f"\n{'='*60}")
        print("TIMESTAMP INVESTIGATION SUMMARY")
        print(f"{'='*60}")
        
        summary = results['summary']
        print(f"Target date: {results['target_date']}")
        print(f"Tables with mixed formats: {summary['tables_with_mixed_formats']}")
        print(f"Tables with timestamp only: {summary['tables_with_timestamp_only']}")
        print(f"Tables with string only: {summary['tables_with_string_only']}")
        print(f"Recommended approach: {summary['recommended_approach']}")
        
        print(f"\nDETAILED ANALYSIS:")
        for table_name, analysis in results['table_analyses'].items():
            print(f"\n📊 {table_name}:")
            
            eventdate_analysis = analysis['eventdate_analysis']
            print(f"  Eventdate types: {eventdate_analysis.get('eventdate_types', {})}")
            print(f"  Date ranges found: {eventdate_analysis.get('date_ranges_found', [])}")
            
            filtering_analysis = analysis['filtering_analysis']
            print(f"  Filtering test results:")
            for method, result in filtering_analysis.get('filtering_tests', {}).items():
                if 'error' in result:
                    print(f"    {method}: ERROR - {result['error']}")
                else:
                    print(f"    {method}: {result.get('row_count', 0)} rows")
        
        if summary['issues_found']:
            print(f"\nISSUES FOUND:")
            for i, issue in enumerate(summary['issues_found'], 1):
                print(f"  {i}. {issue}")
        
        print(f"\n{'='*60}")


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Timestamp Investigation")
    parser.add_argument('--config', help='Configuration file path')
    parser.add_argument('--date', default='2025-06-25', help='Target date (YYYY-MM-DD format)')
    parser.add_argument('--output', default='timestamp_investigation.json', help='Output file')
    
    args = parser.parse_args()
    
    try:
        investigator = TimestampInvestigator(args.config)
        results = investigator.run_investigation(args.date)
        
        # Save results
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # Print summary
        investigator.print_summary(results)
        
        return 0
        
    except Exception as e:
        print(f"❌ Investigation failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
