{"my.app.tngd.polardb": {"table_name": "my.app.tngd.polardb", "test_date": "2025-07-01", "tests": {"date_filtering": {"test_name": "date_filtering", "table_name": "my.app.tngd.polardb", "test_date": "2025-07-01", "issues": ["eventdate value '1751328000107' doesn't match filter '2025-07-01'"], "data": {"backup_query_rows": 5, "total_query_rows": 3, "eventdate_sample": "1751328000107", "date_fields_found": ["eventdate", "raw_message___time__", "raw_message_origin_time", "raw_message_update_rows"]}}, "chunking": {"test_name": "chunking_logic", "table_name": "my.app.tngd.polardb", "test_date": "2025-07-01", "issues": ["Could not get total count"], "data": {}}, "backup_engine": {"test_name": "backup_engine_queries", "table_name": "my.app.tngd.polardb", "test_date": "2025-07-01", "issues": ["Query was modified by _fix_query_syntax - potential issue"], "data": {"constructed_query": "from my.app.tngd.polardb where eventdate = '2025-07-01' select * limit 50000 offset 0", "query_result_count": 1, "query_syntax_valid": true, "extracted_date_range": {"from": "2025-07-01 00:00:00", "to": "2025-07-01 23:59:59"}, "fixed_query": "from my.app.tngd.polardb"}}}, "total_issues": 3, "issues": ["eventdate value '1751328000107' doesn't match filter '2025-07-01'", "Could not get total count", "Query was modified by _fix_query_syntax - potential issue"]}, "cloud.office365.management.exchange": {"table_name": "cloud.office365.management.exchange", "test_date": "2025-07-01", "tests": {"date_filtering": {"test_name": "date_filtering", "table_name": "cloud.office365.management.exchange", "test_date": "2025-07-01", "issues": ["eventdate value '1751328048963' doesn't match filter '2025-07-01'"], "data": {"backup_query_rows": 28, "total_query_rows": 25, "eventdate_sample": "1751328048963", "date_fields_found": ["eventdate", "StatusTime", "timestamp", "Messages_PublishedTime_str", "EndTime", "Schedules_Time_str", "Schedules_TimeZone", "StartTime", "LastUpdatedTime", "MessageTime", "EndTimeUtc", "LastUpdateTimeUtc", "StartTimeUtc"]}}, "chunking": {"test_name": "chunking_logic", "table_name": "cloud.office365.management.exchange", "test_date": "2025-07-01", "issues": ["Could not get total count"], "data": {}}, "backup_engine": {"test_name": "backup_engine_queries", "table_name": "cloud.office365.management.exchange", "test_date": "2025-07-01", "issues": ["Query was modified by _fix_query_syntax - potential issue"], "data": {"constructed_query": "from cloud.office365.management.exchange where eventdate = '2025-07-01' select * limit 50000 offset 0", "query_result_count": 1, "query_syntax_valid": true, "extracted_date_range": {"from": "2025-07-01 00:00:00", "to": "2025-07-01 23:59:59"}, "fixed_query": "from cloud.office365.management.exchange"}}}, "total_issues": 3, "issues": ["eventdate value '1751328048963' doesn't match filter '2025-07-01'", "Could not get total count", "Query was modified by _fix_query_syntax - potential issue"]}, "firewall.fortinet.traffic.forward": {"table_name": "firewall.fortinet.traffic.forward", "test_date": "2025-07-01", "tests": {"date_filtering": {"test_name": "date_filtering", "table_name": "firewall.fortinet.traffic.forward", "test_date": "2025-07-01", "issues": ["eventdate value '1751328000008' doesn't match filter '2025-07-01'"], "data": {"backup_query_rows": 1, "total_query_rows": 1, "eventdate_sample": "1751328000008", "date_fields_found": ["eventdate", "serverdate", "servertime", "timestamp", "eventtime", "serverdatetime"]}}, "chunking": {"test_name": "chunking_logic", "table_name": "firewall.fortinet.traffic.forward", "test_date": "2025-07-01", "issues": ["Could not get total count"], "data": {}}, "backup_engine": {"test_name": "backup_engine_queries", "table_name": "firewall.fortinet.traffic.forward", "test_date": "2025-07-01", "issues": ["Query was modified by _fix_query_syntax - potential issue"], "data": {"constructed_query": "from firewall.fortinet.traffic.forward where eventdate = '2025-07-01' select * limit 50000 offset 0", "query_result_count": 1, "query_syntax_valid": true, "extracted_date_range": {"from": "2025-07-01 00:00:00", "to": "2025-07-01 23:59:59"}, "fixed_query": "from firewall.fortinet.traffic.forward"}}}, "total_issues": 3, "issues": ["eventdate value '1751328000008' doesn't match filter '2025-07-01'", "Could not get total count", "Query was modified by _fix_query_syntax - potential issue"]}}